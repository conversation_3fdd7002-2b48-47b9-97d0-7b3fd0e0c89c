FROM flink:1.18.0-scala_2.12-java11

# Install Python and pip
USER root
RUN apt-get update && \
    apt-get install -y python3 python3-pip python3-dev wget && \
    ln -s /usr/bin/python3 /usr/bin/python && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Download Kafka connector JAR
RUN wget -P /opt/flink/lib/ https://repo1.maven.org/maven2/org/apache/flink/flink-sql-connector-kafka/3.1.0-1.18/flink-sql-connector-kafka-3.1.0-1.18.jar

# Install PyFlink and dependencies
COPY requirements.txt /opt/flink/requirements.txt
RUN pip3 install -r /opt/flink/requirements.txt

# Copy the streaming job
COPY tennis_streaming_job.py /opt/flink/tennis_streaming_job.py

# Create directories for data, checkpoints and savepoints
RUN mkdir -p /opt/flink/data /opt/flink/checkpoints /opt/flink/savepoints && \
    chown -R flink:flink /opt/flink/data /opt/flink/checkpoints /opt/flink/savepoints

USER flink

# Set the working directory
WORKDIR /opt/flink
