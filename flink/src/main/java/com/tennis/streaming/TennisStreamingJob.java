package com.tennis.streaming;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.functions.AggregateFunction;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.connector.file.sink.FileSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.core.fs.Path;
import org.apache.flink.formats.csv.CsvRowSerializationSchema;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.windowing.assigners.TumblingProcessingTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.types.Row;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class TennisStreamingJob {

    public static void main(String[] args) throws Exception {
        // Set up the streaming execution environment
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        
        // Enable checkpointing
        env.enableCheckpointing(10000); // 10 seconds
        
        // Configure Kafka source
        KafkaSource<String> source = KafkaSource.<String>builder()
                .setBootstrapServers("kafka:29092")
                .setTopics("tennis-bets")
                .setGroupId("tennis-streaming-group")
                .setStartingOffsets(OffsetsInitializer.latest())
                .setValueOnlyDeserializer(new SimpleStringSchema())
                .build();

        // Create data stream from Kafka
        DataStream<String> kafkaStream = env.fromSource(source, 
                WatermarkStrategy.noWatermarks(), "Kafka Source");

        // Parse JSON and extract bet information
        DataStream<TennisBet> betStream = kafkaStream
                .map(new JsonToBetMapper())
                .name("Parse JSON to TennisBet");

        // Aggregate by outcome in 10-second windows
        DataStream<BetAggregation> aggregatedStream = betStream
                .keyBy(bet -> bet.outcome)
                .window(TumblingProcessingTimeWindows.of(Time.seconds(10)))
                .aggregate(new BetAggregator())
                .name("Aggregate Bets by Outcome");

        // Convert to CSV format
        DataStream<Row> csvStream = aggregatedStream
                .map(new AggregationToCsvMapper())
                .name("Convert to CSV Row");

        // Configure CSV sink
        CsvRowSerializationSchema csvFormat = CsvRowSerializationSchema.builder()
                .setFieldDelimiter(',')
                .setQuoteCharacter('"')
                .setEscapeCharacter('\\')
                .setNullLiteral("NULL")
                .build();

        // Create file sink with rolling policy
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        FileSink<Row> sink = FileSink
                .forRowFormat(new Path("/opt/flink/data/tennis_bets_" + timestamp), csvFormat)
                .withRollingPolicy(
                    org.apache.flink.connector.file.sink.compaction.FileCompactStrategy
                        .Builder.newBuilder()
                        .enableCompactionOnCheckpoint(5)
                        .build()
                        .getPolicy())
                .build();

        // Add sink to the stream
        csvStream.sinkTo(sink).name("CSV File Sink");

        // Execute the job
        env.execute("Tennis Betting Stream Processing");
    }

    // Data classes
    public static class TennisBet {
        public String timestamp;
        public String outcome;
        public double amount;

        public TennisBet() {}

        public TennisBet(String timestamp, String outcome, double amount) {
            this.timestamp = timestamp;
            this.outcome = outcome;
            this.amount = amount;
        }
    }

    public static class BetAggregation {
        public String outcome;
        public long count;
        public double totalAmount;
        public String windowEnd;

        public BetAggregation() {}

        public BetAggregation(String outcome, long count, double totalAmount, String windowEnd) {
            this.outcome = outcome;
            this.count = count;
            this.totalAmount = totalAmount;
            this.windowEnd = windowEnd;
        }
    }

    // Mappers and Functions
    public static class JsonToBetMapper implements MapFunction<String, TennisBet> {
        private final ObjectMapper objectMapper = new ObjectMapper();

        @Override
        public TennisBet map(String jsonString) throws Exception {
            JsonNode jsonNode = objectMapper.readTree(jsonString);
            return new TennisBet(
                jsonNode.get("timestamp").asText(),
                jsonNode.get("outcome").asText(),
                jsonNode.get("amount").asDouble()
            );
        }
    }

    public static class BetAggregator implements AggregateFunction<TennisBet, Tuple3<String, Long, Double>, BetAggregation> {
        @Override
        public Tuple3<String, Long, Double> createAccumulator() {
            return new Tuple3<>("", 0L, 0.0);
        }

        @Override
        public Tuple3<String, Long, Double> add(TennisBet bet, Tuple3<String, Long, Double> accumulator) {
            return new Tuple3<>(
                bet.outcome,
                accumulator.f1 + 1,
                accumulator.f2 + bet.amount
            );
        }

        @Override
        public BetAggregation getResult(Tuple3<String, Long, Double> accumulator) {
            String windowEnd = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            return new BetAggregation(accumulator.f0, accumulator.f1, accumulator.f2, windowEnd);
        }

        @Override
        public Tuple3<String, Long, Double> merge(Tuple3<String, Long, Double> a, Tuple3<String, Long, Double> b) {
            return new Tuple3<>(a.f0, a.f1 + b.f1, a.f2 + b.f2);
        }
    }

    public static class AggregationToCsvMapper implements MapFunction<BetAggregation, Row> {
        @Override
        public Row map(BetAggregation aggregation) throws Exception {
            Row row = new Row(4);
            row.setField(0, aggregation.windowEnd);
            row.setField(1, aggregation.outcome);
            row.setField(2, aggregation.count);
            row.setField(3, String.format("%.2f", aggregation.totalAmount));
            return row;
        }
    }
}
