#!/bin/bash

# Run Tennis Betting Dashboard Locally

echo "🎾 Executando Tennis Betting Dashboard Localmente"
echo "================================================"

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 não encontrado"
    exit 1
fi

# Check if pip is available
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 não encontrado"
    exit 1
fi

# Check if the main system is running
echo "🔍 Verificando se o sistema principal está rodando..."

if ! docker-compose ps | grep -q "flink-taskmanager.*Up"; then
    echo "❌ Sistema principal não está rodando!"
    echo "💡 Execute primeiro: ./scripts/quick-start.sh"
    exit 1
fi

echo "✅ Sistema principal está rodando"

# Create virtual environment if it doesn't exist
if [ ! -d "dashboard/venv" ]; then
    echo "📦 Criando ambiente virtual..."
    python3 -m venv dashboard/venv
fi

# Activate virtual environment
echo "🔧 Ativando ambiente virtual..."
source dashboard/venv/bin/activate

# Install dependencies
echo "📦 Instalando dependências..."
pip install -r dashboard/requirements.txt

# Run dashboard
echo "🚀 Iniciando dashboard..."
echo ""
echo "🌐 Dashboard será aberto em: http://localhost:8501"
echo "🔄 Para parar: Ctrl+C"
echo ""

cd dashboard
streamlit run tennis_dashboard.py --server.port 8501
