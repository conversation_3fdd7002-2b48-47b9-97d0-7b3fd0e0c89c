#!/bin/bash

# Tennis Betting Stream Processing MVP - Stop Script

echo "🛑 Stopping Tennis Betting Stream Processing MVP..."

# Stop all services
docker-compose down

echo "🧹 Cleaning up..."

# Optional: Remove volumes (uncomment if you want to clean all data)
# docker-compose down -v

echo "✅ All services stopped successfully!"
echo ""
echo "💡 To restart: ./scripts/start.sh"
echo "🗑️  To clean all data: docker-compose down -v"
