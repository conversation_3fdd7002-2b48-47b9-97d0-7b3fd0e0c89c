#!/bin/bash

# Start Tennis Betting Dashboard

echo "🎾 Iniciando Tennis Betting Dashboard"
echo "===================================="

# Check if the main system is running
echo "🔍 Verificando se o sistema principal está rodando..."

if ! docker-compose ps | grep -q "flink-taskmanager.*Up"; then
    echo "❌ Sistema principal não está rodando!"
    echo "💡 Execute primeiro: ./scripts/quick-start.sh"
    exit 1
fi

echo "✅ Sistema principal está rodando"

# Build and start dashboard
echo "🚀 Construindo e iniciando dashboard..."
docker-compose up -d --build tennis-dashboard

# Wait for dashboard to be ready
echo "⏳ Aguardando dashboard ficar pronto..."
sleep 10

# Check if dashboard is running
if docker-compose ps tennis-dashboard | grep -q "Up"; then
    echo "✅ Dashboard iniciado com sucesso!"
    echo ""
    echo "🌐 Acesse o dashboard em:"
    echo "   http://localhost:8501"
    echo ""
    echo "📊 Funcionalidades disponíveis:"
    echo "   - Métricas em tempo real"
    echo "   - Gráficos por outcome"
    echo "   - Timeline de apostas"
    echo "   - Tabela de dados recentes"
    echo "   - Auto-refresh configurável"
    echo ""
    echo "🔧 Comandos úteis:"
    echo "   - Ver logs: docker-compose logs -f tennis-dashboard"
    echo "   - Parar: docker-compose stop tennis-dashboard"
    echo "   - Reiniciar: docker-compose restart tennis-dashboard"
    echo ""
    echo "🎯 O dashboard coleta dados diretamente dos logs do Flink!"
else
    echo "❌ Falha ao iniciar dashboard"
    echo "📋 Verifique os logs: docker-compose logs tennis-dashboard"
    exit 1
fi
