#!/bin/bash

# Tennis Betting Stream Processing MVP - Startup Script
# This script starts the entire streaming pipeline

set -e

echo "🎾 Tennis Betting Stream Processing MVP"
echo "======================================="

# Check if Docker and Docker Compose are available
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed or not in PATH"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed or not in PATH"
    exit 1
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p data flink/checkpoints flink/savepoints

# Start the services
echo "🚀 Starting services with Docker Compose..."
docker-compose up -d zookeeper kafka

echo "⏳ Waiting for Kafka to be ready..."
sleep 30

echo "🔧 Starting Flink cluster..."
docker-compose up -d flink-jobmanager flink-taskmanager

echo "⏳ Waiting for Flink cluster to be ready..."
sleep 20

echo "📊 Starting tennis bet simulator..."
docker-compose up -d tennis-simulator

echo "⏳ Waiting for services to stabilize..."
sleep 10

echo ""
echo "✅ All services are starting up!"
echo ""
echo "🌐 Access points:"
echo "   - Flink Web UI: http://localhost:8081"
echo "   - Data output: ./data/ directory"
echo ""
echo "📋 Useful commands:"
echo "   - View logs: docker-compose logs -f [service-name]"
echo "   - Stop all: docker-compose down"
echo "   - Restart: docker-compose restart [service-name]"
echo ""
echo "🎯 The system will:"
echo "   1. Generate tennis betting events (2 events/second)"
echo "   2. Process them in 10-second windows"
echo "   3. Aggregate by outcome (count + turnover)"
echo "   4. Save results to CSV files in ./data/"
echo ""

# Submit the Flink job
echo "🎯 Submitting Flink job..."
sleep 5

# Wait a bit more and then submit the job
docker-compose exec flink-jobmanager flink run -py /opt/flink/tennis_streaming_job.py

echo ""
echo "🎉 Tennis Betting Stream Processing MVP is now running!"
echo "📈 Check ./data/ directory for CSV output files"
