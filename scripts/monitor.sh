#!/bin/bash

# Monitor script for Tennis Betting Stream Processing MVP

echo "📊 Tennis Betting Stream Processing - Monitor"
echo "============================================"

# Function to check service status
check_service_status() {
    local service=$1
    if docker-compose ps $service | grep -q "Up"; then
        echo "✅ $service"
    else
        echo "❌ $service"
    fi
}

# Function to show latest CSV files
show_latest_csv() {
    echo ""
    echo "📄 Latest CSV Files:"
    if [ -d "data" ] && [ "$(ls -A data/ 2>/dev/null)" ]; then
        ls -lt data/*.csv 2>/dev/null | head -5 | while read line; do
            echo "   $line"
        done
        
        echo ""
        echo "📋 Sample from latest file:"
        latest_file=$(ls -t data/*.csv 2>/dev/null | head -1)
        if [ -n "$latest_file" ] && [ -s "$latest_file" ]; then
            echo "   File: $latest_file"
            head -10 "$latest_file" 2>/dev/null | sed 's/^/   /'
        else
            echo "   (No data yet or file is empty)"
        fi
    else
        echo "   No CSV files found in data/ directory"
    fi
}

# Function to show Kafka message count
show_kafka_stats() {
    echo ""
    echo "📨 Kafka Stats:"
    
    # Try to get message count (this might not work in all environments)
    if docker-compose exec -T kafka kafka-run-class kafka.tools.GetOffsetShell \
        --broker-list localhost:29092 \
        --topic tennis-bets 2>/dev/null | tail -1 | cut -d: -f3 2>/dev/null; then
        local offset=$(docker-compose exec -T kafka kafka-run-class kafka.tools.GetOffsetShell \
            --broker-list localhost:29092 \
            --topic tennis-bets 2>/dev/null | tail -1 | cut -d: -f3 2>/dev/null)
        echo "   Total messages in topic: $offset"
    else
        echo "   Unable to get message count"
    fi
}

# Function to show Flink job status
show_flink_status() {
    echo ""
    echo "🔧 Flink Job Status:"
    
    if docker-compose exec -T flink-jobmanager flink list 2>/dev/null; then
        echo "   ✅ Flink jobs listed above"
    else
        echo "   ❌ Unable to get Flink job status"
    fi
}

# Main monitoring loop
while true; do
    clear
    echo "📊 Tennis Betting Stream Processing - Monitor"
    echo "============================================"
    echo "🕐 $(date)"
    echo ""
    
    echo "🐳 Service Status:"
    check_service_status "zookeeper"
    check_service_status "kafka"
    check_service_status "flink-jobmanager"
    check_service_status "flink-taskmanager"
    check_service_status "tennis-simulator"
    
    show_latest_csv
    show_kafka_stats
    show_flink_status
    
    echo ""
    echo "🔄 Refreshing in 10 seconds... (Ctrl+C to exit)"
    echo "🌐 Flink UI: http://localhost:8081"
    
    sleep 10
done
