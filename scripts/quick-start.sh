#!/bin/bash

# Quick Start Script for Tennis Betting Stream Processing MVP

set -e

echo "🎾 Quick Start - Tennis Betting Stream Processing MVP"
echo "===================================================="

# Function to wait for service
wait_for_service() {
    local service=$1
    local port=$2
    local max_attempts=30
    local attempt=1
    
    echo "⏳ Waiting for $service on port $port..."
    
    while [ $attempt -le $max_attempts ]; do
        if nc -z localhost $port 2>/dev/null; then
            echo "✅ $service is ready!"
            return 0
        fi
        
        echo "   Attempt $attempt/$max_attempts..."
        sleep 2
        ((attempt++))
    done
    
    echo "❌ $service failed to start on port $port"
    return 1
}

# Check prerequisites
echo "🔍 Checking prerequisites..."
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is required but not installed"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is required but not installed"
    exit 1
fi

if ! command -v nc &> /dev/null; then
    echo "⚠️  netcat (nc) not found - will skip port checks"
fi

echo "✅ Prerequisites OK"

# Create directories
echo "📁 Creating directories..."
mkdir -p data flink/checkpoints flink/savepoints

# Step 1: Start Zookeeper and Kafka
echo ""
echo "🚀 Step 1: Starting Zookeeper and Kafka..."
docker-compose up -d zookeeper kafka

if command -v nc &> /dev/null; then
    wait_for_service "Zookeeper" 2181
    wait_for_service "Kafka" 9092
else
    echo "⏳ Waiting 30 seconds for Kafka to be ready..."
    sleep 30
fi

# Step 2: Start Flink cluster
echo ""
echo "🚀 Step 2: Starting Flink cluster..."
docker-compose up -d flink-jobmanager flink-taskmanager

if command -v nc &> /dev/null; then
    wait_for_service "Flink" 8081
else
    echo "⏳ Waiting 20 seconds for Flink to be ready..."
    sleep 20
fi

# Step 3: Start simulator
echo ""
echo "🚀 Step 3: Starting tennis bet simulator..."
docker-compose up -d tennis-simulator

echo "⏳ Waiting 10 seconds for simulator to start..."
sleep 10

# Step 4: Submit Flink job
echo ""
echo "🚀 Step 4: Submitting Flink job..."
echo "   This may take a moment..."

# Try to submit the job with retries
max_retries=3
retry=1

while [ $retry -le $max_retries ]; do
    echo "   Attempt $retry/$max_retries to submit job..."
    
    if docker-compose exec -T flink-jobmanager flink run -py /opt/flink/tennis_streaming_job.py; then
        echo "✅ Flink job submitted successfully!"
        break
    else
        echo "❌ Job submission failed (attempt $retry/$max_retries)"
        if [ $retry -eq $max_retries ]; then
            echo "❌ Failed to submit job after $max_retries attempts"
            echo "📋 Check logs: docker-compose logs flink-jobmanager"
            exit 1
        fi
        ((retry++))
        sleep 5
    fi
done

# Final status
echo ""
echo "🎉 Tennis Betting Stream Processing MVP is running!"
echo ""
echo "📊 System Status:"
echo "   - Kafka: ✅ Running on localhost:9092"
echo "   - Flink UI: ✅ http://localhost:8081"
echo "   - Simulator: ✅ Generating 2 events/second"
echo "   - Output: ✅ CSV files in ./data/"
echo ""
echo "🔧 Useful Commands:"
echo "   - View all logs: docker-compose logs -f"
echo "   - View Flink logs: docker-compose logs -f flink-jobmanager"
echo "   - View simulator logs: docker-compose logs -f tennis-simulator"
echo "   - Stop everything: ./scripts/stop.sh"
echo ""
echo "📈 Expected behavior:"
echo "   - Events are generated every 0.5 seconds (2/sec)"
echo "   - Aggregations happen every 10 seconds"
echo "   - CSV files appear in ./data/ directory"
echo ""
echo "⏰ Give it 1-2 minutes to see the first CSV files!"
