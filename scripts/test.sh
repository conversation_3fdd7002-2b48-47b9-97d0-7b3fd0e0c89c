#!/bin/bash

# Tennis Betting Stream Processing MVP - Test Script

set -e

echo "🧪 Testing Tennis Betting Stream Processing MVP"
echo "=============================================="

# Function to check if service is healthy
check_service() {
    local service=$1
    local max_attempts=30
    local attempt=1
    
    echo "🔍 Checking $service..."
    
    while [ $attempt -le $max_attempts ]; do
        if docker-compose ps $service | grep -q "Up"; then
            echo "✅ $service is running"
            return 0
        fi
        
        echo "⏳ Attempt $attempt/$max_attempts - waiting for $service..."
        sleep 2
        ((attempt++))
    done
    
    echo "❌ $service failed to start"
    return 1
}

# Function to check if Kafka topic exists and has messages
check_kafka_messages() {
    echo "🔍 Checking Kafka messages..."
    
    # Wait a bit for messages to be produced
    sleep 15
    
    # Check if topic has messages
    local message_count=$(docker-compose exec -T kafka kafka-console-consumer \
        --bootstrap-server localhost:29092 \
        --topic tennis-bets \
        --from-beginning \
        --timeout-ms 5000 2>/dev/null | wc -l || echo "0")
    
    if [ "$message_count" -gt 0 ]; then
        echo "✅ Kafka has $message_count messages"
        return 0
    else
        echo "❌ No messages found in Kafka"
        return 1
    fi
}

# Function to check if CSV files are being created
check_csv_output() {
    echo "🔍 Checking CSV output..."
    
    # Wait for processing
    sleep 30
    
    if [ -d "data" ] && [ "$(ls -A data/ 2>/dev/null)" ]; then
        local file_count=$(ls data/*.csv 2>/dev/null | wc -l || echo "0")
        echo "✅ Found $file_count CSV files in data/ directory"
        
        # Show sample of latest file
        local latest_file=$(ls -t data/*.csv 2>/dev/null | head -1)
        if [ -n "$latest_file" ]; then
            echo "📄 Sample from $latest_file:"
            head -5 "$latest_file" 2>/dev/null || echo "   (file is empty or being written)"
        fi
        return 0
    else
        echo "❌ No CSV files found in data/ directory"
        return 1
    fi
}

# Function to check Flink Web UI
check_flink_ui() {
    echo "🔍 Checking Flink Web UI..."
    
    if curl -s http://localhost:8081 > /dev/null; then
        echo "✅ Flink Web UI is accessible at http://localhost:8081"
        return 0
    else
        echo "❌ Flink Web UI is not accessible"
        return 1
    fi
}

# Main test execution
echo "🚀 Starting test sequence..."

# Start services
echo "📦 Building and starting services..."
docker-compose up -d --build

# Check each service
check_service "zookeeper"
check_service "kafka"
check_service "flink-jobmanager"
check_service "flink-taskmanager"
check_service "tennis-simulator"

# Check Flink Web UI
check_flink_ui

# Submit Flink job
echo "🎯 Submitting Flink job..."
sleep 10
docker-compose exec -T flink-jobmanager flink run -py /opt/flink/tennis_streaming_job.py &

# Wait for job to start
sleep 15

# Check if job is running
echo "🔍 Checking Flink job status..."
job_status=$(docker-compose exec -T flink-jobmanager flink list 2>/dev/null | grep "RUNNING" || echo "")
if [ -n "$job_status" ]; then
    echo "✅ Flink job is running"
else
    echo "❌ Flink job is not running"
    echo "📋 Job list:"
    docker-compose exec -T flink-jobmanager flink list
fi

# Check Kafka messages
check_kafka_messages

# Check CSV output
check_csv_output

echo ""
echo "🎉 Test completed!"
echo ""
echo "📊 Summary:"
echo "   - Services: $(docker-compose ps --services | wc -l) containers"
echo "   - Flink UI: http://localhost:8081"
echo "   - Data output: ./data/ directory"
echo ""
echo "🔧 To stop everything: ./scripts/stop.sh"
echo "📋 To view logs: docker-compose logs -f"
