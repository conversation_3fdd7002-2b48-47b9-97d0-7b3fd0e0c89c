# 🏗️ Arquitetura do Sistema

## Visão Geral

O MVP de processamento de apostas de tênis em tempo real é composto por 4 componentes principais orquestrados via Docker Compose:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Simulador     │───▶│     Kafka       │───▶│     Flink       │───▶│   CSV Files     │
│    Python       │    │   (Message      │    │  (PyFlink)      │    │   (Local)       │
│                 │    │    Broker)      │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Componentes Detalhados

### 1. <PERSON>mu<PERSON><PERSON> <PERSON>as (Python)
- **Localização**: `simulator/tennis_bet_simulator.py`
- **Função**: Gera eventos de apostas de tênis realistas
- **Taxa**: 2 eventos por segundo (configurável)
- **Formato**: JSON com timestamp, outcome, amount, match_id, user_id, odds

#### Outcomes Simulados:
- `player1_win` / `player2_win`
- `over_2.5_sets` / `under_2.5_sets`
- `first_set_player1` / `first_set_player2`

#### Distribuição de Valores:
- 40% apostas pequenas (R$ 10-50)
- 30% apostas médias (R$ 100-500)
- 20% apostas grandes (R$ 1.000-5.000)
- 10% apostas muito grandes (R$ 10.000+)

### 2. Apache Kafka
- **Versão**: Confluent Platform 7.4.0
- **Tópico**: `tennis-bets`
- **Partições**: 1 (configurável)
- **Replicação**: 1 (single broker)
- **Porta**: 9092 (externa), 29092 (interna)

### 3. Apache Flink (PyFlink)
- **Versão**: 1.18.0
- **Linguagem**: Python 3.11
- **Paralelismo**: 2
- **Checkpointing**: 10 segundos
- **Janelas**: Tumbling de 10 segundos

#### Processamento:
1. **Source**: Kafka Consumer
2. **Parse**: JSON → TennisBet objects
3. **KeyBy**: Agrupamento por outcome
4. **Window**: Janelas de 10 segundos
5. **Aggregate**: Count + Sum por outcome
6. **Sink**: CSV files + Console output

### 4. Armazenamento CSV
- **Localização**: `./data/`
- **Formato**: CSV com header
- **Colunas**: window_end, outcome, count, total_amount
- **Rolling**: A cada 1MB ou 1 minuto

## Fluxo de Dados

### 1. Geração de Eventos
```json
{
  "timestamp": "2024-06-14T12:34:56.789",
  "outcome": "player1_win",
  "amount": 150.75,
  "match_id": "match_5",
  "user_id": "user_1234",
  "odds": 2.35
}
```

### 2. Processamento em Janelas
- **Janela**: 10 segundos (tumbling)
- **Chave**: outcome
- **Agregação**: COUNT(*), SUM(amount)

### 3. Saída CSV
```csv
window_end,outcome,count,total_amount
2024-06-14 12:34:50,player1_win,15,2847.50
2024-06-14 12:34:50,player2_win,12,1923.75
```

## Configurações de Tolerância a Falhas

### Checkpointing
- **Intervalo**: 10 segundos
- **Diretório**: `/opt/flink/checkpoints`
- **Estratégia**: Exactly-once processing

### Kafka
- **Offset Management**: Automático
- **Group ID**: `tennis-streaming-group`
- **Starting Position**: Earliest (para testes)

### File Sink
- **Rolling Policy**: 
  - Tamanho: 1MB
  - Tempo: 1 minuto
  - Inatividade: 15 segundos

## Monitoramento

### Métricas Disponíveis
1. **Flink Web UI** (http://localhost:8081)
   - Throughput
   - Latência
   - Backpressure
   - Checkpoints

2. **Logs Docker**
   - `docker-compose logs -f [service]`

3. **Arquivos CSV**
   - Contagem de registros
   - Timestamps das janelas

### Scripts de Monitoramento
- `./scripts/monitor.sh` - Monitor em tempo real
- `./scripts/quick-start.sh` - Inicialização rápida
- `./scripts/test.sh` - Testes automatizados

## Escalabilidade

### Horizontal
- **Kafka**: Adicionar partições e brokers
- **Flink**: Aumentar paralelismo e task managers
- **Simulador**: Múltiplas instâncias

### Vertical
- **Memória**: Ajustar heap size do Flink
- **CPU**: Mais cores para task managers
- **Disco**: SSD para checkpoints

## Configurações de Produção

### Kafka
```yaml
KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 3
KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 3
KAFKA_NUM_PARTITIONS: 6
```

### Flink
```yaml
parallelism.default: 4
taskmanager.numberOfTaskSlots: 4
state.backend: rocksdb
state.checkpoints.num-retained: 10
```

### Recursos
```yaml
resources:
  limits:
    memory: "2Gi"
    cpu: "1000m"
  requests:
    memory: "1Gi"
    cpu: "500m"
```

## Próximas Melhorias

1. **Observabilidade**
   - Prometheus metrics
   - Grafana dashboards
   - Alerting

2. **Dados**
   - Schema Registry
   - Avro serialization
   - Data validation

3. **Deployment**
   - Kubernetes manifests
   - Helm charts
   - CI/CD pipeline

4. **Performance**
   - Benchmarking
   - Load testing
   - Optimization
