# 🔧 Troubleshooting Guide

## Problemas Comuns e Soluções

### 1. Serviços não iniciam

#### Problema: Docker Compose falha
```bash
# Verificar se Docker está rodando
sudo systemctl status docker

# Verificar se há conflitos de porta
sudo netstat -tulpn | grep -E ':(2181|8081|9092)'

# Limpar containers antigos
docker-compose down -v
docker system prune -f
```

#### Problema: Kafka não conecta
```bash
# Verificar logs do Kafka
docker-compose logs kafka

# Verificar se Zookeeper está rodando
docker-compose logs zookeeper

# Testar conectividade
docker-compose exec kafka kafka-topics --bootstrap-server localhost:29092 --list
```

### 2. Flink Job não executa

#### Problema: Job falha ao submeter
```bash
# Verificar logs do JobManager
docker-compose logs flink-jobmanager

# Verificar se TaskManager está conectado
docker-compose logs flink-taskmanager

# Verificar no Web UI
curl http://localhost:8081/taskmanagers
```

#### Problema: PyFlink dependencies
```bash
# Reconstruir imagem Flink
docker-compose build --no-cache flink-jobmanager

# Verificar se JAR do Kafka foi baixado
docker-compose exec flink-jobmanager ls -la /opt/flink/lib/

# Testar Python no container
docker-compose exec flink-jobmanager python -c "import pyflink; print('OK')"
```

### 3. Simulador não produz eventos

#### Problema: Kafka connection refused
```bash
# Verificar se Kafka está acessível
docker-compose exec tennis-simulator nc -zv kafka 29092

# Verificar logs do simulador
docker-compose logs tennis-simulator

# Testar produção manual
docker-compose exec kafka kafka-console-producer --bootstrap-server localhost:29092 --topic tennis-bets
```

#### Problema: Tópico não existe
```bash
# Criar tópico manualmente
docker-compose exec kafka kafka-topics --bootstrap-server localhost:29092 --create --topic tennis-bets --partitions 1 --replication-factor 1

# Listar tópicos
docker-compose exec kafka kafka-topics --bootstrap-server localhost:29092 --list
```

### 4. Nenhum arquivo CSV é gerado

#### Problema: Diretório de saída
```bash
# Verificar se diretório existe
ls -la data/

# Verificar permissões
sudo chown -R $USER:$USER data/

# Verificar logs do Flink
docker-compose logs flink-taskmanager | grep -i csv
```

#### Problema: Job não processa dados
```bash
# Verificar se há mensagens no Kafka
docker-compose exec kafka kafka-console-consumer --bootstrap-server localhost:29092 --topic tennis-bets --from-beginning --timeout-ms 5000

# Verificar status do job
docker-compose exec flink-jobmanager flink list

# Verificar métricas no Web UI
curl http://localhost:8081/jobs
```

### 5. Performance Issues

#### Problema: Baixo throughput
```bash
# Aumentar paralelismo
# Editar docker-compose.yml:
# FLINK_PROPERTIES: |
#   parallelism.default: 4
#   taskmanager.numberOfTaskSlots: 4

# Aumentar rate do simulador
docker-compose up -d --env EVENTS_PER_SECOND=10.0 tennis-simulator
```

#### Problema: Backpressure
```bash
# Verificar no Web UI
curl http://localhost:8081/jobs/{job-id}/vertices/{vertex-id}/backpressure

# Aumentar buffer size
# Adicionar ao FLINK_PROPERTIES:
# taskmanager.network.memory.fraction: 0.2
# taskmanager.network.memory.max: 1gb
```

### 6. Comandos de Diagnóstico

#### Status dos Serviços
```bash
# Status geral
docker-compose ps

# Uso de recursos
docker stats

# Logs em tempo real
docker-compose logs -f --tail=100
```

#### Kafka Diagnostics
```bash
# Verificar consumer groups
docker-compose exec kafka kafka-consumer-groups --bootstrap-server localhost:29092 --list

# Verificar lag do consumer
docker-compose exec kafka kafka-consumer-groups --bootstrap-server localhost:29092 --describe --group tennis-streaming-group

# Verificar offsets
docker-compose exec kafka kafka-run-class kafka.tools.GetOffsetShell --broker-list localhost:29092 --topic tennis-bets
```

#### Flink Diagnostics
```bash
# Listar jobs
docker-compose exec flink-jobmanager flink list

# Cancelar job
docker-compose exec flink-jobmanager flink cancel <job-id>

# Verificar checkpoints
docker-compose exec flink-jobmanager ls -la /opt/flink/checkpoints/

# Verificar savepoints
docker-compose exec flink-jobmanager flink savepoint <job-id>
```

### 7. Limpeza e Reset

#### Reset Completo
```bash
# Parar tudo
docker-compose down

# Remover volumes
docker-compose down -v

# Limpar dados locais
rm -rf data/* flink/checkpoints/* flink/savepoints/*

# Limpar imagens
docker-compose build --no-cache

# Reiniciar
./scripts/quick-start.sh
```

#### Reset Apenas Dados
```bash
# Parar processamento
docker-compose stop tennis-simulator flink-jobmanager flink-taskmanager

# Limpar dados
rm -rf data/*

# Resetar offsets do Kafka
docker-compose exec kafka kafka-consumer-groups --bootstrap-server localhost:29092 --reset-offsets --to-earliest --group tennis-streaming-group --topic tennis-bets --execute

# Reiniciar
docker-compose start flink-jobmanager flink-taskmanager tennis-simulator
```

### 8. Monitoramento Avançado

#### Verificar Conectividade de Rede
```bash
# Testar conectividade entre containers
docker-compose exec tennis-simulator ping kafka
docker-compose exec flink-jobmanager ping kafka

# Verificar DNS resolution
docker-compose exec tennis-simulator nslookup kafka
```

#### Verificar Recursos
```bash
# Memória e CPU
docker-compose exec flink-jobmanager free -h
docker-compose exec flink-jobmanager top -bn1

# Espaço em disco
docker-compose exec flink-jobmanager df -h
```

### 9. Logs Importantes

#### Localização dos Logs
```bash
# Flink JobManager
docker-compose logs flink-jobmanager | grep -E "(ERROR|WARN|Exception)"

# Flink TaskManager
docker-compose logs flink-taskmanager | grep -E "(ERROR|WARN|Exception)"

# Simulador
docker-compose logs tennis-simulator | grep -E "(ERROR|WARN|Exception)"

# Kafka
docker-compose logs kafka | grep -E "(ERROR|WARN|Exception)"
```

### 10. Contato e Suporte

Se os problemas persistirem:

1. Colete logs: `docker-compose logs > debug.log`
2. Verifique configurações: `docker-compose config`
3. Documente o erro específico
4. Verifique versões: `docker --version && docker-compose --version`

#### Informações Úteis para Debug
- Sistema operacional
- Versão do Docker
- Recursos disponíveis (RAM, CPU, disco)
- Logs específicos do erro
- Passos para reproduzir o problema
