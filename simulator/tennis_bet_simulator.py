#!/usr/bin/env python3

import json
import random
import time
import logging
from datetime import datetime
from typing import Dict, Any
import os

from kafka import KafkaProducer
from kafka.errors import KafkaError

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TennisBetSimulator:
    def __init__(self, bootstrap_servers: str, topic: str):
        self.bootstrap_servers = bootstrap_servers
        self.topic = topic
        self.producer = None
        
        # Tennis betting outcomes
        self.outcomes = [
            'player1_win',
            'player2_win',
            'over_2.5_sets',
            'under_2.5_sets',
            'first_set_player1',
            'first_set_player2'
        ]
        
        # Betting amounts distribution (more realistic)
        self.bet_amounts = [
            (10.0, 0.4),    # 40% chance of small bets (10-50)
            (100.0, 0.3),   # 30% chance of medium bets (100-500)
            (1000.0, 0.2),  # 20% chance of large bets (1000-5000)
            (10000.0, 0.1)  # 10% chance of very large bets (10000+)
        ]

    def connect_kafka(self) -> bool:
        """Connect to Kafka with retry logic"""
        max_retries = 10
        retry_delay = 5
        
        for attempt in range(max_retries):
            try:
                self.producer = KafkaProducer(
                    bootstrap_servers=self.bootstrap_servers,
                    value_serializer=lambda v: json.dumps(v).encode('utf-8'),
                    key_serializer=lambda k: k.encode('utf-8') if k else None,
                    acks='all',
                    retries=3,
                    batch_size=16384,
                    linger_ms=10,
                    buffer_memory=33554432
                )
                logger.info(f"Successfully connected to Kafka at {self.bootstrap_servers}")
                return True
                
            except Exception as e:
                logger.warning(f"Attempt {attempt + 1}/{max_retries} failed to connect to Kafka: {e}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                else:
                    logger.error("Failed to connect to Kafka after all retries")
                    return False
        
        return False

    def generate_bet_amount(self) -> float:
        """Generate realistic bet amounts with weighted distribution"""
        rand = random.random()
        cumulative_prob = 0.0
        
        for base_amount, probability in self.bet_amounts:
            cumulative_prob += probability
            if rand <= cumulative_prob:
                # Add some randomness to the base amount
                multiplier = random.uniform(0.1, 5.0)
                return round(base_amount * multiplier, 2)
        
        # Fallback
        return round(random.uniform(10.0, 100.0), 2)

    def generate_bet_event(self) -> Dict[str, Any]:
        """Generate a single tennis bet event"""
        return {
            'timestamp': datetime.now().isoformat(),
            'outcome': random.choice(self.outcomes),
            'amount': self.generate_bet_amount(),
            'match_id': f"match_{random.randint(1, 10)}",
            'user_id': f"user_{random.randint(1000, 9999)}",
            'odds': round(random.uniform(1.1, 5.0), 2)
        }

    def send_bet_event(self, event: Dict[str, Any]) -> bool:
        """Send a bet event to Kafka"""
        try:
            # Use outcome as key for better partitioning
            key = event['outcome']
            
            future = self.producer.send(
                self.topic,
                key=key,
                value=event
            )
            
            # Wait for the message to be sent (with timeout)
            record_metadata = future.get(timeout=10)
            
            logger.debug(f"Sent event to topic {record_metadata.topic} "
                        f"partition {record_metadata.partition} "
                        f"offset {record_metadata.offset}")
            return True
            
        except KafkaError as e:
            logger.error(f"Failed to send event to Kafka: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error sending event: {e}")
            return False

    def run_simulation(self, events_per_second: float = 2.0, duration_seconds: int = None):
        """Run the betting simulation"""
        if not self.connect_kafka():
            logger.error("Cannot start simulation - Kafka connection failed")
            return

        logger.info(f"Starting tennis betting simulation...")
        logger.info(f"Events per second: {events_per_second}")
        logger.info(f"Duration: {'unlimited' if duration_seconds is None else f'{duration_seconds} seconds'}")
        
        event_count = 0
        start_time = time.time()
        interval = 1.0 / events_per_second
        
        try:
            while True:
                # Check duration limit
                if duration_seconds and (time.time() - start_time) >= duration_seconds:
                    break
                
                # Generate and send event
                event = self.generate_bet_event()
                if self.send_bet_event(event):
                    event_count += 1
                    
                    if event_count % 10 == 0:
                        logger.info(f"Sent {event_count} events. Latest: {event['outcome']} - ${event['amount']}")
                
                # Wait for next event
                time.sleep(interval)
                
        except KeyboardInterrupt:
            logger.info("Simulation interrupted by user")
        except Exception as e:
            logger.error(f"Simulation error: {e}")
        finally:
            if self.producer:
                self.producer.flush()
                self.producer.close()
            
            elapsed_time = time.time() - start_time
            logger.info(f"Simulation completed. Sent {event_count} events in {elapsed_time:.2f} seconds")


def main():
    # Get configuration from environment variables
    bootstrap_servers = os.getenv('KAFKA_BOOTSTRAP_SERVERS', 'localhost:9092')
    topic = os.getenv('KAFKA_TOPIC', 'tennis-bets')
    events_per_second = float(os.getenv('EVENTS_PER_SECOND', '2.0'))
    duration = os.getenv('DURATION_SECONDS')
    
    duration_seconds = int(duration) if duration else None
    
    logger.info(f"Configuration:")
    logger.info(f"  Kafka Bootstrap Servers: {bootstrap_servers}")
    logger.info(f"  Topic: {topic}")
    logger.info(f"  Events per second: {events_per_second}")
    logger.info(f"  Duration: {duration_seconds or 'unlimited'} seconds")
    
    # Create and run simulator
    simulator = TennisBetSimulator(bootstrap_servers, topic)
    simulator.run_simulation(events_per_second, duration_seconds)


if __name__ == "__main__":
    main()
