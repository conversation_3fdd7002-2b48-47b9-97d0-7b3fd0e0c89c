FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy the simulator script
COPY tennis_bet_simulator.py .

# Make the script executable
RUN chmod +x tennis_bet_simulator.py

# Set default environment variables
ENV KAFKA_BOOTSTRAP_SERVERS=kafka:29092
ENV KAFKA_TOPIC=tennis-bets
ENV EVENTS_PER_SECOND=2.0

# Run the simulator
CMD ["python", "tennis_bet_simulator.py"]
