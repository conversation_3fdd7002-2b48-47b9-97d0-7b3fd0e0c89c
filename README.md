# 🎾 Tennis Betting Stream Processing MVP

Um MVP dockerizado com Apache Flink para processar eventos de apostas de tênis em tempo real, agregando métricas a cada 10 segundos e salvando os resultados em CSV.

## 🏗️ Arquitetura

```
Simulador Python → Kafka → PyFlink → CSV Local
```

### Componentes

- **Simulador**: Gera eventos de apostas de tênis realistas
- **Apache Kafka**: Message broker para streaming de eventos
- **Apache Flink (PyFlink)**: Processamento de stream em tempo real
- **CSV Output**: Resultados agregados salvos localmente

## 📊 Métricas Agregadas

A cada janela de 10 segundos, o sistema calcula por `outcome`:

1. **Volume de apostas**: Número total de apostas
2. **Turnover**: Soma total do dinheiro apostado

### Outcomes de Tênis

- `player1_win` / `player2_win`
- `over_2.5_sets` / `under_2.5_sets`
- `first_set_player1` / `first_set_player2`

## 🚀 Como Executar

### Pré-requisitos

- Docker
- Docker Compose

### Inicialização Rápida

```bash
# Tornar scripts executáveis
chmod +x scripts/*.sh

# Opção 1: Inicialização automática (recomendado)
./scripts/quick-start.sh

# Opção 2: Inicialização completa com mais logs
./scripts/start.sh
```

### Inicialização Manual

```bash
# 1. Subir Kafka e Zookeeper
docker-compose up -d zookeeper kafka

# 2. Aguardar Kafka ficar pronto (30s)
sleep 30

# 3. Subir cluster Flink
docker-compose up -d flink-jobmanager flink-taskmanager

# 4. Aguardar Flink ficar pronto (20s)
sleep 20

# 5. Iniciar simulador
docker-compose up -d tennis-simulator

# 6. Submeter job Flink
docker-compose exec flink-jobmanager flink run -py /opt/flink/tennis_streaming_job.py
```

## 📈 Monitoramento

### Interfaces Web

- **Flink Web UI**: http://localhost:8081
  - Visualizar jobs, métricas, checkpoints
  - Monitorar throughput e latência

### Logs

```bash
# Ver logs de todos os serviços
docker-compose logs -f

# Ver logs específicos
docker-compose logs -f flink-jobmanager
docker-compose logs -f tennis-simulator
docker-compose logs -f kafka
```

### Dados de Saída

Os arquivos CSV são salvos em `./data/` com formato:

```csv
window_end,outcome,count,total_amount
2024-06-14 12:34:50,player1_win,15,2847.50
2024-06-14 12:34:50,over_2.5_sets,8,1234.75
```

## ⚙️ Configuração

### Variáveis de Ambiente

#### Simulador
- `EVENTS_PER_SECOND`: Taxa de eventos (padrão: 2.0)
- `DURATION_SECONDS`: Duração em segundos (padrão: ilimitado)
- `KAFKA_BOOTSTRAP_SERVERS`: Servidores Kafka
- `KAFKA_TOPIC`: Tópico Kafka (padrão: tennis-bets)

#### Flink
- Checkpointing: 10 segundos
- Paralelismo: 2
- Janelas: 10 segundos (Tumbling)

### Personalização

```bash
# Alterar taxa de eventos
docker-compose up -d --env EVENTS_PER_SECOND=5.0 tennis-simulator

# Executar por tempo limitado
docker-compose up -d --env DURATION_SECONDS=300 tennis-simulator
```

## 🛠️ Desenvolvimento

### Estrutura do Projeto

```
.
├── docker-compose.yml          # Orquestração dos serviços
├── flink/
│   ├── Dockerfile             # Container Flink customizado
│   ├── requirements.txt       # Dependências PyFlink
│   └── tennis_streaming_job.py # Job de processamento
├── simulator/
│   ├── Dockerfile             # Container simulador
│   ├── requirements.txt       # Dependências Python
│   └── tennis_bet_simulator.py # Gerador de eventos
├── scripts/
│   ├── start.sh              # Script de inicialização
│   └── stop.sh               # Script de parada
├── data/                     # Saída CSV (criado automaticamente)
└── README.md
```

### Modificar o Job Flink

1. Editar `flink/tennis_streaming_job.py`
2. Reconstruir container: `docker-compose build flink-jobmanager`
3. Reiniciar: `docker-compose restart flink-jobmanager flink-taskmanager`
4. Resubmeter job

### Modificar o Simulador

1. Editar `simulator/tennis_bet_simulator.py`
2. Reconstruir: `docker-compose build tennis-simulator`
3. Reiniciar: `docker-compose restart tennis-simulator`

## 🔧 Troubleshooting

### Problemas Comuns

1. **Kafka não conecta**
   ```bash
   # Verificar se Kafka está rodando
   docker-compose ps kafka
   
   # Ver logs do Kafka
   docker-compose logs kafka
   ```

2. **Flink job falha**
   ```bash
   # Ver logs do JobManager
   docker-compose logs flink-jobmanager
   
   # Verificar no Web UI: http://localhost:8081
   ```

3. **Sem arquivos CSV**
   ```bash
   # Verificar se o diretório existe
   ls -la data/
   
   # Ver logs do TaskManager
   docker-compose logs flink-taskmanager
   ```

### Limpeza Completa

```bash
# Parar e remover tudo
./scripts/stop.sh

# Remover volumes e dados
docker-compose down -v
rm -rf data/* flink/checkpoints/* flink/savepoints/*
```

## 📋 Comandos Úteis

```bash
# Status dos serviços
docker-compose ps

# Parar tudo
./scripts/stop.sh

# Ver uso de recursos
docker stats

# Acessar container Flink
docker-compose exec flink-jobmanager bash

# Listar jobs Flink
docker-compose exec flink-jobmanager flink list

# Cancelar job Flink
docker-compose exec flink-jobmanager flink cancel <job-id>
```

## 🎯 Próximos Passos

- [ ] Adicionar métricas Prometheus
- [ ] Implementar alertas
- [ ] Dashboard Grafana
- [ ] Testes automatizados
- [ ] CI/CD pipeline
- [ ] Escalabilidade horizontal
